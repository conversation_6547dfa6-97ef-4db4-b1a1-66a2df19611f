<div class="container-fluid p-4">
  <div class="row">
    <!-- Product Details Card -->
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-body">
          <!-- Header with Title and Buttons -->
          <div class="d-flex justify-content-between align-items-center mb-4">
            <h5 class="card-title mb-0 text-dark fw-semibold">Product Details</h5>
            <div class="d-flex gap-2">
              <button type="button" class="btn btn-primary px-4" (click)="onView()">View</button>
              <button type="button" class="btn btn-outline-secondary px-4" (click)="onReset()">Reset</button>
            </div>
          </div>

          <div class="row">
            <!-- Left Column - Filters -->
            <div class="col-md-6">
              <div class="row g-3">
                <!-- First Row of Dropdowns -->
                <div class="col-md-6">
                  <select 
                    class="form-select" 
                    [(ngModel)]="selectedCommonBlend"
                    aria-label="Common Blend">
                    <option value="">Common Blend (If Any)*</option>
                    <option value="blend1">Blend Option 1</option>
                    <option value="blend2">Blend Option 2</option>
                    <option value="blend3">Blend Option 3</option>
                  </select>
                </div>
                <div class="col-md-6">
                  <select 
                    class="form-select" 
                    [(ngModel)]="selectedSemiFinished"
                    aria-label="Semi Finished">
                    <option value="">Semi Finished*</option>
                    <option value="semi1">Semi Finished Option 1</option>
                    <option value="semi2">Semi Finished Option 2</option>
                    <option value="semi3">Semi Finished Option 3</option>
                  </select>
                </div>

                <!-- Second Row - Date Inputs -->
                <div class="col-md-6">
                  <div class="input-group">
                    <input 
                      type="date" 
                      class="form-control" 
                      [(ngModel)]="fromDate"
                      placeholder="From*" 
                      aria-label="From date">
                    <span class="input-group-text">
                      <i class="bi bi-calendar3"></i>
                    </span>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="input-group">
                    <input 
                      type="date" 
                      class="form-control" 
                      [(ngModel)]="toDate"
                      placeholder="To*" 
                      aria-label="To date">
                    <span class="input-group-text">
                      <i class="bi bi-calendar3"></i>
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Column - Finished Goods -->
            <div class="col-md-6">
              <div class="card bg-light border-0 h-100">
                <div class="card-header bg-transparent border-0 pb-2">
                  <h6 class="mb-0 text-muted">Finished Goods</h6>
                </div>
                <div class="card-body pt-2">
                  <!-- Finished Goods Content -->
                  <div *ngIf="finishedGoods.length === 0; else finishedGoodsContent" 
                       class="text-center text-muted py-5">
                    <i class="bi bi-box-seam fs-1 opacity-25"></i>
                    <p class="mt-2 mb-0">No finished goods data available</p>
                  </div>
                  
                  <ng-template #finishedGoodsContent>
                    <div class="list-group list-group-flush">
                      <div *ngFor="let item of finishedGoods" 
                           class="list-group-item bg-transparent border-0 px-0 py-2">
                        <div class="d-flex justify-content-between align-items-center">
                          <span class="fw-medium">{{ item.name }}</span>
                          <span class="badge bg-primary rounded-pill">{{ item.quantity }}</span>
                        </div>
                        <small class="text-muted">{{ item.description }}</small>
                      </div>
                    </div>
                  </ng-template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
