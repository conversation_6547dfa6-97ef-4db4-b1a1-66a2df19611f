import { Component } from '@angular/core';

export interface FinishedGood {
  id: number;
  name: string;
  quantity: number;
  description: string;
}

@Component({
  selector: 'app-product-details',
  templateUrl: './product-details.component.html',
  styleUrls: ['./product-details.component.css']
})
export class ProductDetailsComponent {
  // Form properties
  selectedCommonBlend: string = '';
  selectedSemiFinished: string = '';
  fromDate: string = '';
  toDate: string = '';

  // Finished goods data
  finishedGoods: FinishedGood[] = [
    // Hardcoded sample data - replace with actual data
    {
      id: 1,
      name: 'Product A',
      quantity: 150,
      description: 'High quality finished product'
    },
    {
      id: 2,
      name: 'Product B',
      quantity: 75,
      description: 'Premium grade material'
    },
    {
      id: 3,
      name: 'Product C',
      quantity: 200,
      description: 'Standard quality product'
    }
  ];

  constructor() { }

  onView(): void {
    // Handle view button click
    console.log('View clicked with filters:', {
      commonBlend: this.selectedCommonBlend,
      semiFinished: this.selectedSemiFinished,
      fromDate: this.fromDate,
      toDate: this.toDate
    });

    // Add your view logic here
    // For example: call a service to fetch filtered data
    this.fetchFilteredData();
  }

  onReset(): void {
    // Reset all form fields
    this.selectedCommonBlend = '';
    this.selectedSemiFinished = '';
    this.fromDate = '';
    this.toDate = '';
    
    // Reset finished goods to initial state or empty
    this.finishedGoods = [];
    
    console.log('Form reset');
  }

  private fetchFilteredData(): void {
    // Simulate API call or data filtering
    // Replace this with actual service call
    
    if (this.selectedCommonBlend || this.selectedSemiFinished || this.fromDate || this.toDate) {
      // Simulate filtered results
      this.finishedGoods = [
        {
          id: 1,
          name: 'Filtered Product A',
          quantity: 100,
          description: 'Filtered result based on criteria'
        },
        {
          id: 2,
          name: 'Filtered Product B',
          quantity: 50,
          description: 'Another filtered result'
        }
      ];
    } else {
      // Show all products if no filters
      this.finishedGoods = [
        {
          id: 1,
          name: 'Product A',
          quantity: 150,
          description: 'High quality finished product'
        },
        {
          id: 2,
          name: 'Product B',
          quantity: 75,
          description: 'Premium grade material'
        },
        {
          id: 3,
          name: 'Product C',
          quantity: 200,
          description: 'Standard quality product'
        }
      ];
    }
  }
}
