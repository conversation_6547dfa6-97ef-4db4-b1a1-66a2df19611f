/* Custom styles for Product Details Component */

.card {
  border: none;
  border-radius: 8px;
}

.card-title {
  color: #2c3e50;
  font-weight: 600;
}

.btn-primary {
  background-color: #4f46e5;
  border-color: #4f46e5;
  font-weight: 500;
}

.btn-primary:hover {
  background-color: #4338ca;
  border-color: #4338ca;
}

.btn-outline-secondary {
  color: #6b7280;
  border-color: #d1d5db;
  font-weight: 500;
}

.btn-outline-secondary:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
}

.form-select {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.form-select:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
}

.form-control {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.form-control:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
}

.input-group-text {
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  color: #6b7280;
}

.finished-goods-card {
  background-color: #f8fafc;
  min-height: 200px;
}

.list-group-item {
  transition: background-color 0.2s ease;
}

.list-group-item:hover {
  background-color: rgba(79, 70, 229, 0.05) !important;
}

.badge {
  font-size: 0.75rem;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }
  
  .btn {
    width: 100%;
  }
  
  .container-fluid {
    padding: 1rem !important;
  }
}

/* Custom scrollbar for finished goods list */
.card-body {
  max-height: 300px;
  overflow-y: auto;
}

.card-body::-webkit-scrollbar {
  width: 6px;
}

.card-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.card-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.card-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
