<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Details</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid p-4">
        <div class="row">
            <!-- Product Details Card -->
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <!-- Header with Title and Buttons -->
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="card-title mb-0 text-dark fw-semibold">Product Details</h5>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-primary px-4">View</button>
                                <button type="button" class="btn btn-outline-secondary px-4">Reset</button>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Left Column - Filters -->
                            <div class="col-md-6">
                                <div class="row g-3">
                                    <!-- First Row of Dropdowns -->
                                    <div class="col-md-6">
                                        <select class="form-select" aria-label="Common Blend">
                                            <option selected>Common Blend (If Any)*</option>
                                            <option value="1">Blend Option 1</option>
                                            <option value="2">Blend Option 2</option>
                                            <option value="3">Blend Option 3</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-select" aria-label="Semi Finished">
                                            <option selected>Semi Finished*</option>
                                            <option value="1">Semi Finished Option 1</option>
                                            <option value="2">Semi Finished Option 2</option>
                                            <option value="3">Semi Finished Option 3</option>
                                        </select>
                                    </div>

                                    <!-- Second Row - Date Inputs -->
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <input type="text" class="form-control" placeholder="From*" aria-label="From date">
                                            <span class="input-group-text">
                                                <i class="bi bi-calendar3"></i>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <input type="text" class="form-control" placeholder="To*" aria-label="To date">
                                            <span class="input-group-text">
                                                <i class="bi bi-calendar3"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column - Finished Goods -->
                            <div class="col-md-6">
                                <div class="card bg-light border-0 h-100">
                                    <div class="card-header bg-transparent border-0 pb-2">
                                        <h6 class="mb-0 text-muted">Finished Goods</h6>
                                    </div>
                                    <div class="card-body pt-2">
                                        <!-- This area would contain the finished goods content -->
                                        <div class="text-center text-muted py-5">
                                            <i class="bi bi-box-seam fs-1 opacity-25"></i>
                                            <p class="mt-2 mb-0">No finished goods data available</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Optional: Add date picker functionality -->
    <script>
        // You can add date picker initialization here if needed
        // For example, using a library like flatpickr or bootstrap-datepicker
        
        // Example with basic date input type change
        document.addEventListener('DOMContentLoaded', function() {
            const dateInputs = document.querySelectorAll('input[placeholder*="From"], input[placeholder*="To"]');
            dateInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.type = 'date';
                });
                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.type = 'text';
                    }
                });
            });
        });
    </script>
</body>
</html>
